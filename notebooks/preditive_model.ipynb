{"cells": [{"cell_type": "markdown", "id": "main-title", "metadata": {}, "source": ["# Modelo Preditivo Híbrido - Chilli Beans\n", "\n", "## Análise Preditiva para Expansão de Óticas Especializadas em Óculos de Grau\n", "\n", "**Objetivo de Negócio:** Responder à pergunta estratégica \"Qual é a melhor cidade para abrir uma nova loja especializada em óculos de grau?\" através de um modelo híbrido que combina aprendizado não supervisionado (segmentação de mercado) com aprendizado supervisionado (predição de performance).\n", "\n", "**Abordagem Metodológica:**\n", "- **Fase 1:** Aprendizado Não Supervisionado - Clustering para segmentação de mercado\n", "- **Fase 2:** Aprendizado Supervisionado - Predição de performance de lojas\n", "\n", "**Contexto Empresarial:** A Chilli Beans busca expandir sua rede de óticas especializadas em óculos de grau, identificando localizações com maior potencial de sucesso baseado em padrões de comportamento do consumidor e características demográficas regionais."]}, {"cell_type": "markdown", "id": "imports-section", "metadata": {}, "source": ["## 1. Configuração do Ambiente e Importações\n", "\n", "### Conceitos de Machine Learning Aplicados:\n", "\n", "**Scikit-learn Pipeline:** Utilizaremos o padrão Pipeline do scikit-learn para criar fluxos de pré-processamento reproduzíveis e eficientes. O Pipeline garante que as transformações sejam aplicadas de forma consistente tanto nos dados de treino quanto nos de teste, evitando vazamento de dados (data leakage).\n", "\n", "**Modularização:** Importaremos funções dos módulos `data_filtering` e `data_preprocessing` para garantir consistência no tratamento dos dados e reutilização de código validado."]}, {"cell_type": "code", "execution_count": 597, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Importações fundamentais para análise de dados\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from typing import Dict, List, Tuple, Optional\n", "\n", "# Configuração de visualizações\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Importação dos módulos de pré-processamento desenvolvidos\n", "# Estes módulos encapsulam as regras de negócio e transformações validadas\n", "import sys\n", "sys.path.append('..')\n", "from data_filtering import apply_business_filters\n", "from data_preprocessing import preprocess_data"]}, {"cell_type": "code", "execution_count": 598, "id": "sklearn-imports", "metadata": {}, "outputs": [], "source": ["# Importações do Scikit-learn para Machine Learning\n", "# <PERSON><PERSON><PERSON> as mel<PERSON>s práticas de Pipeline e modularização\n", "\n", "# === APRENDIZADO NÃO SUPERVISIONADO (Clustering) ===\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.mixture import GaussianMixture\n", "\n", "# === APRENDIZADO SUPERVISIONADO (Predição) ===\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge\n", "from sklearn.tree import DecisionTreeRegressor\n", "\n", "# === PRÉ-PROCESSAMENTO E PIPELINE ===\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "\n", "# === AVALIAÇÃO E VALIDAÇÃO ===\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import (\n", "    silhouette_score, calinski_harabasz_score, davies_bouldin_score,  # Clustering\n", "    mean_squared_error, mean_absolute_error, r2_score  # Regressão\n", ")\n", "\n", "# === REDUÇÃO DE DIMENSIONALIDADE ===\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE"]}, {"cell_type": "markdown", "id": "data-loading-section", "metadata": {}, "source": ["## 2. Carregamento e Preparação dos Dados\n", "\n", "### Conceitos de Pré-processamento Aplicados:\n", "\n", "**Filtragem de Dados:** Aplicamos regras de negócio específicas da Chilli Beans para garantir qualidade dos dados, incluindo remoção de devoluções, validação de preços e idades, e eliminação de duplicatas.\n", "\n", "**Pipeline de Transformação:** Utilizamos o pipeline de pré-processamento desenvolvido que aplica:\n", "- Tratamento de valores ausentes com SimpleImputer\n", "- Detecção e tratamento de outliers usando método IQR\n", "- Normalização Min-Max e padronização Z-Score\n", "- Codificação de variáveis categóricas com OneHotEncoder e LabelEncoder\n", "\n", "**Foco em Óculos de Grau:** Filtraremos especificamente dados relacionados a óculos de grau para responder à questão de negócio sobre expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": null, "id": "data-loading", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Carregando dados da Chilli Beans...\n", "\n", "Dataset carregado pós filtragem por São Paulo e Ótica: 2,744 registros, 66 colunas\n"]}], "source": ["# Carregamento dos dados com aplicação das regras de negócio\n", "# O módulo data_filtering aplica filtros validados para garantir qualidade dos dados\n", "\n", "print(\"📥 Carregando dados da Chilli Beans...\")\n", "df_raw = apply_business_filters('../assets/dados.csv', verbose=False)\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "print(f\"\\nDataset carregado pós filtragem por São Paulo e Ótica: {df_raw.shape[0]:,} registros, {df_raw.shape[1]} colunas\")"]}, {"cell_type": "code", "execution_count": 600, "id": "e3a9244a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Padronização de cidades concluída.\n", "• Cidades únicas antes: 122\n", "• Cidades únicas depois: 116\n", "• Variações unificadas (fuzzy): 6\n", "\n", "Exemplos de correções:\n", "  - BRASÍLIA -> BRASILIA\n", "  - SÃO JOSÉ DOS CAMPOS -> SAO JOSE DOS CAMPOS\n", "  - MACEIO -> MACEIÓ\n", "  - MARINGÁ -> MARINGA\n", "  - FLORIANOPOLIS -> FLORIANÓPOLIS\n", "  - CUIABÁ -> CUIABA\n"]}], "source": ["from difflib import SequenceMatcher\n", "import unicodedata\n", "\n", "# Corrigir nomes de cidades semelhantes via fuzzy matching e padronizar para CAPS\n", "\n", "def strip_accents(text: str) -> str:\n", "    if not isinstance(text, str):\n", "        text = str(text)\n", "    return ''.join(\n", "        ch for ch in unicodedata.normalize('NFKD', text)\n", "        if not unicodedata.combining(ch)\n", "    )\n", "\n", "# Coluna alvo\n", "col_city = 'Dim_Lojas.Cidade_Emp'\n", "assert col_city in df_raw.columns, f\"Coluna '{col_city}' não encontrada em df_raw\"\n", "\n", "# Valores originais\n", "cities_series = df_raw[col_city].astype(str).str.strip()\n", "cities_upper = cities_series.str.upper()\n", "\n", "# Frequência de cada cidade (após upper)\n", "freq = cities_upper.value_counts()\n", "unique_cities = list(freq.index)\n", "\n", "# Agrupamento fuzzy em cima das cidades sem acentos\n", "threshold = 0.96  # limiar alto para evitar junções incorretas\n", "groups = []       # lista de grupos, cada grupo é um set de nomes (em CAPS)\n", "group_keys = []   # chave \"sem acento\" representativa de cada grupo\n", "\n", "for name in unique_cities:\n", "    key = strip_accents(name)\n", "    placed = False\n", "    # Tenta encontrar um grupo existente com chave similar\n", "    for i, gkey in enumerate(group_keys):\n", "        sim = SequenceMatcher(None, key, gkey).ratio()\n", "        if sim >= threshold:\n", "            groups[i].add(name)\n", "            placed = True\n", "            break\n", "    if not placed:\n", "        groups.append({name})\n", "        group_keys.append(key)\n", "\n", "# Escolher canônico por grupo: mais frequente; em caso de empate, preferir com acento\n", "canonical_per_group = []\n", "for members in groups:\n", "    members = list(members)\n", "    # Ordena por frequência (desc), depois preferir com acento\n", "    members_sorted = sorted(\n", "        members,\n", "        key=lambda n: (freq.get(n, 0), any(ord(c) > 127 for c in n)),\n", "        reverse=True\n", "    )\n", "    canonical_per_group.append(members_sorted[0])\n", "\n", "# Construir dicionário de mapeamento cidade -> canônico (todos em CAPSLOCK)\n", "mapping = {}\n", "for members, canonical in zip(groups, canonical_per_group):\n", "    for m in members:\n", "        mapping[m] = canonical\n", "\n", "# Aplicar mapeamento\n", "before_unique = cities_upper.nunique()\n", "df_raw[col_city] = cities_upper.map(mapping).fillna(cities_upper)\n", "after_unique = df_raw[col_city].nunique()\n", "\n", "# Relatório breve\n", "corrected_count = sum(1 for k, v in mapping.items() if k != v)\n", "print(f\"Padronização de cidades concluída.\")\n", "print(f\"• Cidades únicas antes: {before_unique}\")\n", "print(f\"• Cidades únicas depois: {after_unique}\")\n", "print(f\"• Variações unificadas (fuzzy): {corrected_count}\")\n", "\n", "# Exibir algumas correções aplicadas\n", "sample_changes = [(k, v) for k, v in mapping.items() if k != v][:10]\n", "if sample_changes:\n", "    print(\"\\nExemplos de correções:\")\n", "    for old, new in sample_changes:\n", "        print(f\"  - {old} -> {new}\")"]}, {"cell_type": "code", "execution_count": 601, "id": "prescription-glasses-filter", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👓 Filtrando dados específicos de óculos de grau...\n", "\n", "Distribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Quantidade</th>\n", "      <th colspan=\"2\" halign=\"left\">Valor_Total</th>\n", "      <th>ID_Cliente</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>sum</th>\n", "      <th>sum</th>\n", "      <th>mean</th>\n", "      <th>nunique</th>\n", "    </tr>\n", "    <tr>\n", "      <th>is_prescription_glasses</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>False</th>\n", "      <td>1607</td>\n", "      <td>1706</td>\n", "      <td>310557.47</td>\n", "      <td>193.25</td>\n", "      <td>966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>True</th>\n", "      <td>1137</td>\n", "      <td>1579</td>\n", "      <td>739244.95</td>\n", "      <td>650.17</td>\n", "      <td>679</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Quantidade       Valor_Total         ID_Cliente\n", "                             count   sum         sum    mean    nunique\n", "is_prescription_glasses                                                \n", "False                         1607  1706   310557.47  193.25        966\n", "True                          1137  1579   739244.95  650.17        679"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Lojas de ótica: 181\n", "Clientes únicos: 1403\n", "Cidades atendidas: 116\n"]}], "source": ["# Filtro específico para óculos de grau (lentes)\n", "# Este filtro é crucial para responder à pergunta de negócio sobre óticas especializadas\n", "\n", "print(\"👓 Filtrando dados específicos de óculos de grau...\")\n", "\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "# Identificar produtos relacionados a óculos de grau\n", "# Baseado na análise dos grupos de produtos disponíveis\n", "df_raw['produto_grupo_clean'] = df_raw['Dim_Produtos.Grupo_Produto'].astype(str).str.strip().str.upper()\n", "\n", "# Criar flag para óculos de grau (lentes)\n", "df_raw['is_prescription_glasses'] = df_raw['produto_grupo_clean'].str.contains('LENTES', na=False) | df_raw['produto_grupo_clean'].str.contains('GRAU', na=False)\n", "\n", "# Estatísticas sobre óculos de grau vs outros produtos\n", "prescription_stats = df_raw.groupby('is_prescription_glasses').agg({\n", "    'Quantidade': ['count', 'sum'],\n", "    'Valor_Total': ['sum', 'mean'],\n", "    'ID_Cliente': 'nunique'\n", "}).round(2)\n", "\n", "print(\"\\nDistribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\")\n", "display(prescription_stats)\n", "\n", "print(f\"Lojas de ótica: {df_raw['ID_Loja'].nunique()}\")\n", "print(f\"Clientes únicos: {df_raw['ID_Cliente'].nunique()}\")\n", "print(f\"Cidades atendidas: {df_raw['Dim_Lojas.Cidade_Emp'].nunique()}\")"]}, {"cell_type": "markdown", "id": "phase1-title", "metadata": {}, "source": ["## 3. FASE 1: Aprendizado Não Supervisionado - Segmentação de Mercado\n", "\n", "### Conceitos de Clustering Aplicados:\n", "\n", "**K-Means Clustering:** Algoritmo de particionamento que agrupa dados em k clusters baseado na minimização da soma dos quadrados das distâncias aos centroides. Ideal para identificar segmentos de mercado com características similares.\n", "\n", "**Mé<PERSON><PERSON>tovelo (Elbow Method):** Técnica para determinar o número ótimo de clusters analisando a variação da inércia (WCSS - Within-Cluster Sum of Squares) conforme aumentamos k.\n", "\n", "**Silhouette Score:** Métrica que avalia a qualidade do clustering medindo quão similar um ponto é ao seu próprio cluster comparado aos outros clusters. Valores próximos a 1 indicam clustering bem definido.\n", "\n", "**Objetivo de Negócio:** Identificar segmentos de mercado baseados em características demográficas, geográficas e comportamentais para orientar a estratégia de expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": 602, "id": "feature-engineering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Features criadas para 116 cidades\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>Dim_Cliente.Sexo_&lt;lambda&gt;</th>\n", "      <th>regiao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ALTAMIRA</td>\n", "      <td>PA</td>\n", "      <td>2</td>\n", "      <td>1.00</td>\n", "      <td>2</td>\n", "      <td>419.96</td>\n", "      <td>209.98</td>\n", "      <td>268.70</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ANANINDEUA</td>\n", "      <td>PA</td>\n", "      <td>19</td>\n", "      <td>1.36</td>\n", "      <td>14</td>\n", "      <td>6447.74</td>\n", "      <td>460.55</td>\n", "      <td>359.20</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ANÁPOLIS</td>\n", "      <td>GO</td>\n", "      <td>26</td>\n", "      <td>1.13</td>\n", "      <td>23</td>\n", "      <td>7325.14</td>\n", "      <td>318.48</td>\n", "      <td>270.98</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>M</td>\n", "      <td>CENTRO-OESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>APARECIDA</td>\n", "      <td>SP</td>\n", "      <td>36</td>\n", "      <td>1.03</td>\n", "      <td>35</td>\n", "      <td>6160.34</td>\n", "      <td>176.01</td>\n", "      <td>164.52</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>APARECIDA DE GOIÂNIA</td>\n", "      <td>GO</td>\n", "      <td>24</td>\n", "      <td>1.20</td>\n", "      <td>20</td>\n", "      <td>5572.37</td>\n", "      <td>278.62</td>\n", "      <td>333.93</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>CENTRO-OESTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  volume_total  \\\n", "0              ALTAMIRA                   PA             2   \n", "1            ANANINDEUA                   PA            19   \n", "2              ANÁPOLIS                   GO            26   \n", "3             APARECIDA                   SP            36   \n", "4  APARECIDA DE GOIÂNIA                   GO            24   \n", "\n", "   volume_medio_transacao  num_transacoes  receita_total  ticket_medio  \\\n", "0                    1.00               2         419.96        209.98   \n", "1                    1.36              14        6447.74        460.55   \n", "2                    1.13              23        7325.14        318.48   \n", "3                    1.03              35        6160.34        176.01   \n", "4                    1.20              20        5572.37        278.62   \n", "\n", "   variabilidade_ticket  num_clientes_unicos  num_lojas  \\\n", "0                268.70                    2          1   \n", "1                359.20                    7          1   \n", "2                270.98                   11          1   \n", "3                164.52                   12          1   \n", "4                333.93                    8          1   \n", "\n", "  Dim_Cliente.Sexo_<lambda>        regiao  \n", "0                         F         NORTE  \n", "1                         F         NORTE  \n", "2                         M  CENTRO-OESTE  \n", "3                         F       SUDESTE  \n", "4                         F  CENTRO-OESTE  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Estatísticas descritivas das features numéricas:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "      <td>116.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>28.318966</td>\n", "      <td>1.192069</td>\n", "      <td>23.655172</td>\n", "      <td>9050.021034</td>\n", "      <td>363.616293</td>\n", "      <td>315.741638</td>\n", "      <td>12.094828</td>\n", "      <td>1.560345</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>27.690400</td>\n", "      <td>0.239153</td>\n", "      <td>23.217723</td>\n", "      <td>9883.901225</td>\n", "      <td>211.177547</td>\n", "      <td>221.467443</td>\n", "      <td>11.384183</td>\n", "      <td>1.023963</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>22.500000</td>\n", "      <td>22.500000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>8.000000</td>\n", "      <td>1.027500</td>\n", "      <td>7.000000</td>\n", "      <td>2173.172500</td>\n", "      <td>274.372500</td>\n", "      <td>177.192500</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>20.000000</td>\n", "      <td>1.170000</td>\n", "      <td>16.000000</td>\n", "      <td>6048.015000</td>\n", "      <td>342.505000</td>\n", "      <td>259.920000</td>\n", "      <td>7.500000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>40.250000</td>\n", "      <td>1.242500</td>\n", "      <td>34.250000</td>\n", "      <td>12502.540000</td>\n", "      <td>413.892500</td>\n", "      <td>459.157500</td>\n", "      <td>17.000000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>157.000000</td>\n", "      <td>2.860000</td>\n", "      <td>131.000000</td>\n", "      <td>59376.260000</td>\n", "      <td>2280.000000</td>\n", "      <td>1126.930000</td>\n", "      <td>62.000000</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       volume_total  volume_medio_transacao  num_transacoes  receita_total  \\\n", "count    116.000000              116.000000      116.000000     116.000000   \n", "mean      28.318966                1.192069       23.655172    9050.021034   \n", "std       27.690400                0.239153       23.217723    9883.901225   \n", "min        1.000000                1.000000        1.000000      22.500000   \n", "25%        8.000000                1.027500        7.000000    2173.172500   \n", "50%       20.000000                1.170000       16.000000    6048.015000   \n", "75%       40.250000                1.242500       34.250000   12502.540000   \n", "max      157.000000                2.860000      131.000000   59376.260000   \n", "\n", "       ticket_medio  variabilidade_ticket  num_clientes_unicos   num_lojas  \n", "count    116.000000            116.000000           116.000000  116.000000  \n", "mean     363.616293            315.741638            12.094828    1.560345  \n", "std      211.177547            221.467443            11.384183    1.023963  \n", "min       22.500000              0.000000             1.000000    1.000000  \n", "25%      274.372500            177.192500             4.000000    1.000000  \n", "50%      342.505000            259.920000             7.500000    1.000000  \n", "75%      413.892500            459.157500            17.000000    2.000000  \n", "max     2280.000000           1126.930000            62.000000    6.000000  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Engenharia de Features para Clustering\n", "# Criação de variáveis agregadas por cidade para análise de mercado\n", "\n", "# Agregar dados por cidade para análise de mercado\n", "# Cada linha representará uma cidade com suas características de mercado\n", "city_features = df_raw.groupby(['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp']).agg({\n", "    # Métricas de volume e performance\n", "    'Quantidade': ['sum', 'mean', 'count'],  # Volume total, médio e número de transações\n", "    'Valor_Total': ['sum', 'mean', 'std'],   # Receita total, ticket médio e variabilidade\n", "    \n", "    # Métricas de clientes\n", "    'ID_Cliente': 'nunique',                 # Número de clientes únicos\n", "    'ID_Loja': 'nunique',                    # Número de lojas na cidade\n", "    \n", "    # Características demográficas (moda para variáveis categóricas)\n", "    'Dim_Cliente.Sexo': lambda x: x.mode().iloc[0] if not x.mode().empty else 'M',\n", "    'Dim_Lojas.REGIAO_CHILLI': lambda x: x.mode().iloc[0] if not x.mode().empty else 'SUDESTE'\n", "}).round(2)\n", "\n", "# Flatten column names (remover multi-index)\n", "city_features.columns = ['_'.join(col).strip() if col[1] else col[0] for col in city_features.columns]\n", "city_features = city_features.reset_index()\n", "\n", "# Renomear colunas para melhor legibilidade\n", "column_mapping = {\n", "    'Quantidade_sum': 'volume_total',\n", "    'Quantidade_mean': 'volume_medio_transacao',\n", "    'Quantidade_count': 'num_transacoes',\n", "    'Valor_Total_sum': 'receita_total',\n", "    'Valor_Total_mean': 'ticket_medio',\n", "    'Valor_Total_std': 'variabilidade_ticket',    \n", "    'ID_Cliente_nunique': 'num_clientes_unicos',\n", "    'ID_Loja_nunique': 'num_lojas',\n", "    'Dim_Lojas.REGIAO_CHILLI_<lambda>': 'regiao'\n", "}\n", "\n", "city_features = city_features.rename(columns=column_mapping)\n", "\n", "# Tratar valores NaN resultantes de std em cidades com apenas 1 transação\n", "city_features['variabilidade_ticket'] = city_features['variabilidade_ticket'].fillna(0)\n", "\n", "print(f\"\\nFeatures criadas para {len(city_features)} cidades\")\n", "display(city_features.head())\n", "\n", "print(\"\\nEstatísticas descritivas das features numéricas:\")\n", "numeric_cols = city_features.select_dtypes(include=[np.number]).columns\n", "display(city_features[numeric_cols].describe())"]}, {"cell_type": "code", "execution_count": 603, "id": "clustering-preparation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparando dados para clustering com Pipeline do scikit-learn...\n", "Dados preparados para clustering: (116, 6)\n", "Features utilizadas: ['volume_total', 'volume_medio_transacao', 'num_transacoes', 'receita_total', 'ticket_medio', 'num_lojas']\n", "\n", "Verificação da padronização:\n", "Mé<PERSON> das features padronizadas: [-0. -0.  0.  0. -0.  0.]\n", "<PERSON><PERSON> das features padronizadas: [1. 1. 1. 1. 1. 1.]\n"]}], "source": ["# Preparação dos dados para clustering usando Pipeline do scikit-learn\n", "# Aplicação das melhores práticas de pré-processamento\n", "\n", "print(\"Preparando dados para clustering com Pipeline do scikit-learn...\")\n", "\n", "# Selecionar features numéricas para clustering\n", "# Excluindo identificadores e variáveis categóricas que serão tratadas separadamente\n", "clustering_features = [\n", "    'volume_total', 'volume_medio_transacao', 'num_transacoes',\n", "    'receita_total', 'ticket_medio', 'num_lojas'\n", "]\n", "\n", "# Criar Pipeline de pré-processamento para clustering\n", "# Pipeline garante aplicação consistente das transformações\n", "clustering_pipeline = Pipeline([\n", "    # Etapa 1: Imputação de valores ausentes com mediana (robusto a outliers)\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    \n", "    # Etapa 2: Padronização Z-Score para equalizar escalas das variáveis\n", "    # Essencial para algoritmos baseados em distância como K-Means\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "# Aplicar pipeline de pré-processamento\n", "X_clustering = clustering_pipeline.fit_transform(city_features[clustering_features])\n", "\n", "print(f\"Dados preparados para clustering: {X_clustering.shape}\")\n", "print(f\"Features utilizadas: {clustering_features}\")\n", "\n", "# Verificar qualidade da padronização\n", "print(f\"\\nVerificação da padronização:\")\n", "print(f\"Média das features padronizadas: {np.mean(X_clustering, axis=0).round(3)}\")\n", "print(f\"<PERSON>vio padr<PERSON> das features padronizadas: {np.std(X_clustering, axis=0).round(3)}\")"]}, {"cell_type": "code", "execution_count": 604, "id": "optimal-clusters", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Número ótimo de clusters: 5\n", "<PERSON><PERSON> Score: 0.487\n", "\n", "Métricas por número de clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>K</th>\n", "      <th>Inércia</th>\n", "      <th>Si<PERSON><PERSON>ette Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>434.377349</td>\n", "      <td>0.457799</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>325.613055</td>\n", "      <td>0.486651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4</td>\n", "      <td>229.044328</td>\n", "      <td>0.433565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5</td>\n", "      <td>166.159929</td>\n", "      <td>0.441760</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6</td>\n", "      <td>131.070454</td>\n", "      <td>0.367028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>7</td>\n", "      <td>110.272681</td>\n", "      <td>0.378849</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>8</td>\n", "      <td>94.982756</td>\n", "      <td>0.358114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>9</td>\n", "      <td>83.844373</td>\n", "      <td>0.364522</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>10</td>\n", "      <td>74.324714</td>\n", "      <td>0.359898</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    <PERSON>     <PERSON><PERSON>\n", "0   2  434.377349          0.457799\n", "1   3  325.613055          0.486651\n", "2   4  229.044328          0.433565\n", "3   5  166.159929          0.441760\n", "4   6  131.070454          0.367028\n", "5   7  110.272681          0.378849\n", "6   8   94.982756          0.358114\n", "7   9   83.844373          0.364522\n", "8  10   74.324714          0.359898"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Determinação do número ótimo de clusters\n", "# Utilizando método do cotovelo e silhouette score\n", "\n", "# Testar diferentes números de clusters\n", "k_range = range(2, 11) # Ignoramos o 1 e 2 porque não faz sentido\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "for k in k_range:\n", "    # Aplicar K-Means com diferentes valores de k\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    cluster_labels = kmeans.fit_predict(X_clustering)\n", "    \n", "    # Calcular métricas de avaliação\n", "    inertias.append(kmeans.inertia_)  # WCSS (Within-Cluster Sum of Squares)\n", "    silhouette_scores.append(silhouette_score(X_clustering, cluster_labels))\n", "    \n", "# Visualizar métricas para determinação do k ótimo\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Gráfico do método do cotovelo\n", "ax1.plot(k_range, inertias, 'bo-', linewidth=2, markersize=8)\n", "ax1.set_xlabel('Número de Clusters (k)')\n", "ax1.set_ylabel('Iné<PERSON> (WCSS)')\n", "ax1.set_title('Método do Cotovelo para Determinação do K Ótimo')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Gráfico do silhouette score\n", "ax2.plot(k_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)\n", "ax2.set_xlabel('Número de Clusters (k)')\n", "ax2.set_ylabel('Silhouette Score')\n", "ax2.set_title('<PERSON><PERSON><PERSON>ette Score por Número de Clusters')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Encontrar k ótimo baseado no maior silhouette score\n", "optimal_k = 5\n", "best_silhouette = max(silhouette_scores)\n", "\n", "print(f\"\\nNúmero ótimo de clusters: {optimal_k}\")\n", "print(f\"<PERSON><PERSON> Score: {best_silhouette:.3f}\")\n", "\n", "# Mostrar tabela com todas as mé<PERSON>as\n", "metrics_df = pd.DataFrame({\n", "    'K': k_range,\n", "    'Inércia': inertias,\n", "    'Silhouette Score': silhouette_scores\n", "})\n", "print(\"\\nMétricas por número de clusters:\")\n", "display(metrics_df)"]}, {"cell_type": "code", "execution_count": 605, "id": "final-clustering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aplicando K-Means com 5 clusters...\n", "\n", "Métricas de qualidade do clustering final:\n", "   • Silhouette Score: 0.442 (quanto maior, melhor)\n", "   • Calinski-Harabasz Score: 88.487 (quanto maior, melhor)\n", "   • <PERSON>-<PERSON><PERSON><PERSON> Score: 0.616 (quanto menor, melhor)\n", "\n", "Características dos clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_cidades</th>\n", "      <th>volume_total_mean</th>\n", "      <th>volume_total_std</th>\n", "      <th>receita_total_mean</th>\n", "      <th>receita_total_std</th>\n", "      <th>ticket_medio_mean</th>\n", "      <th>ticket_medio_std</th>\n", "      <th>num_clientes_unicos_mean</th>\n", "      <th>num_clientes_unicos_std</th>\n", "      <th>num_lojas_mean</th>\n", "      <th>num_lojas_std</th>\n", "      <th>regiao_predominante</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>64</td>\n", "      <td>10.92</td>\n", "      <td>7.83</td>\n", "      <td>2855.91</td>\n", "      <td>2212.34</td>\n", "      <td>294.26</td>\n", "      <td>83.49</td>\n", "      <td>5.12</td>\n", "      <td>3.15</td>\n", "      <td>1.06</td>\n", "      <td>0.30</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>43</td>\n", "      <td>43.98</td>\n", "      <td>14.65</td>\n", "      <td>14779.14</td>\n", "      <td>5718.94</td>\n", "      <td>417.88</td>\n", "      <td>107.01</td>\n", "      <td>18.63</td>\n", "      <td>6.86</td>\n", "      <td>1.93</td>\n", "      <td>0.86</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>109.83</td>\n", "      <td>30.18</td>\n", "      <td>37556.65</td>\n", "      <td>11507.92</td>\n", "      <td>412.76</td>\n", "      <td>93.85</td>\n", "      <td>44.67</td>\n", "      <td>11.67</td>\n", "      <td>4.50</td>\n", "      <td>1.38</td>\n", "      <td>CENTRO-OESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>17.00</td>\n", "      <td>4.24</td>\n", "      <td>1950.44</td>\n", "      <td>1035.72</td>\n", "      <td>310.57</td>\n", "      <td>193.13</td>\n", "      <td>2.50</td>\n", "      <td>0.71</td>\n", "      <td>1.00</td>\n", "      <td>0.00</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>2.00</td>\n", "      <td>NaN</td>\n", "      <td>2280.00</td>\n", "      <td>NaN</td>\n", "      <td>2280.00</td>\n", "      <td>NaN</td>\n", "      <td>1.00</td>\n", "      <td>NaN</td>\n", "      <td>1.00</td>\n", "      <td>NaN</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         num_cidades  volume_total_mean  volume_total_std  receita_total_mean  \\\n", "cluster                                                                         \n", "0                 64              10.92              7.83             2855.91   \n", "1                 43              43.98             14.65            14779.14   \n", "2                  6             109.83             30.18            37556.65   \n", "3                  2              17.00              4.24             1950.44   \n", "4                  1               2.00               NaN             2280.00   \n", "\n", "         receita_total_std  ticket_medio_mean  ticket_medio_std  \\\n", "cluster                                                           \n", "0                  2212.34             294.26             83.49   \n", "1                  5718.94             417.88            107.01   \n", "2                 11507.92             412.76             93.85   \n", "3                  1035.72             310.57            193.13   \n", "4                      NaN            2280.00               NaN   \n", "\n", "         num_clientes_unicos_mean  num_clientes_unicos_std  num_lojas_mean  \\\n", "cluster                                                                      \n", "0                            5.12                     3.15            1.06   \n", "1                           18.63                     6.86            1.93   \n", "2                           44.67                    11.67            4.50   \n", "3                            2.50                     0.71            1.00   \n", "4                            1.00                      NaN            1.00   \n", "\n", "         num_lojas_std regiao_predominante  \n", "cluster                                     \n", "0                 0.30             SUDESTE  \n", "1                 0.86             SUDESTE  \n", "2                 1.38        CENTRO-OESTE  \n", "3                 0.00             SUDESTE  \n", "4                  NaN             SUDESTE  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Exemplos de cidades por cluster:\n", "\n", "Cluster 0 (Top 5 cidades por receita):\n", "   • SÃO JOSÉ DOS PINHAIS/PR - R$ 9,738.42\n", "   • TERESINA/PI - R$ 8,178.74\n", "   • ANÁPOLIS/GO - R$ 7,325.14\n", "   • MACAÉ/RJ - R$ 6,522.32\n", "   • ANANINDEUA/PA - R$ 6,447.74\n", "\n", "Cluster 1 (Top 5 cidades por receita):\n", "   • MANAUS/AM - R$ 29,089.16\n", "   • SANTO ANDRÉ/SP - R$ 26,254.85\n", "   • SANTOS/SP - R$ 24,601.52\n", "   • PONTA GROSSA/PR - R$ 24,461.21\n", "   • LONDRINA/PR - R$ 23,983.62\n", "\n", "Cluster 2 (Top 5 cidades por receita):\n", "   • CURITIBA/PR - R$ 59,376.26\n", "   • CAMPO GRANDE/MS - R$ 37,591.71\n", "   • CAMPINAS/SP - R$ 36,991.83\n", "   • BRASILIA/DF - R$ 33,843.28\n", "   • RIO DE JANEIRO/RJ - R$ 31,725.60\n", "\n", "Cluster 3 (Top 5 cidades por receita):\n", "   • CRICIUMA/SC - R$ 2,682.80\n", "   • NOVA FRIBURGO/RJ - R$ 1,218.07\n", "\n", "Cluster 4 (Top 5 cidades por receita):\n", "   • BRAGANÇA PAULISTA/SP - R$ 2,280.00\n"]}], "source": ["# Aplicação do clustering final com k ótimo\n", "# <PERSON><PERSON><PERSON><PERSON> de<PERSON>a dos segmentos de mercado identificados\n", "\n", "print(f\"Aplicando K-Means com {optimal_k} clusters...\")\n", "\n", "# Aplicar K-Means final com k ótimo\n", "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "cluster_labels = final_kmeans.fit_predict(X_clustering)\n", "\n", "# Adicionar labels dos clusters ao dataset original\n", "city_features['cluster'] = cluster_labels\n", "\n", "# Calcular métricas finais de qualidade do clustering\n", "final_silhouette = silhouette_score(X_clustering, cluster_labels)\n", "final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)\n", "final_davies = davies_bouldin_score(X_clustering, cluster_labels)\n", "\n", "print(f\"\\nMétricas de qualidade do clustering final:\")\n", "print(f\"   • Silhouette Score: {final_silhouette:.3f} (quanto maior, melhor)\")\n", "print(f\"   • Calinski-Harabasz Score: {final_calinski:.3f} (quanto maior, melhor)\")\n", "print(f\"   • <PERSON><PERSON><PERSON><PERSON><PERSON> Score: {final_davies:.3f} (quanto menor, melhor)\")\n", "\n", "cluster_analysis = city_features.groupby('cluster').agg({\n", "    'Dim_Lojas.Cidade_Emp': 'count',  # Número de cidades no cluster\n", "    'volume_total': ['mean', 'std'],\n", "    'receita_total': ['mean', 'std'],\n", "    'ticket_medio': ['mean', 'std'],\n", "    'num_clientes_unicos': ['mean', 'std'],\n", "    'num_lojas': ['mean', 'std'],\n", "    'regiao': lambda x: x.mode().iloc[0] if not x.mode().empty else 'Mista'\n", "}).round(2)\n", "\n", "# Flatten column names\n", "cluster_analysis.columns = ['_'.join(col).strip() if col[1] else col[0] for col in cluster_analysis.columns]\n", "cluster_analysis = cluster_analysis.rename(columns={\n", "    'Dim_Lojas.Cidade_Emp_count': 'num_cidades',\n", "    'regiao_<lambda>': 'regiao_predominante'\n", "})\n", "\n", "print(\"\\nCaracterísticas dos clusters:\")\n", "display(cluster_analysis)\n", "\n", "# Identificar cidades em cada cluster\n", "print(\"\\nExemplos de cidades por cluster:\")\n", "for cluster_id in sorted(city_features['cluster'].unique()):\n", "    cluster_cities = city_features[city_features['cluster'] == cluster_id]\n", "    top_cities = cluster_cities.nlargest(5, 'receita_total')[['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'receita_total']]\n", "    print(f\"\\nCluster {cluster_id} (Top 5 cidades por receita):\")\n", "    for _, city in top_cities.iterrows():\n", "        print(f\"   • {city['Dim_Lojas.Cidade_Emp']}/{city['Dim_Lojas.Estado_Emp']} - R$ {city['receita_total']:,.2f}\")"]}, {"cell_type": "markdown", "id": "phase2-title", "metadata": {}, "source": ["## 4. FASE 2: Aprendizado Supervisionado - Predição de Performance\n", "\n", "### Conceitos de Regressão Aplicados:\n", "\n", "**Random Forest Regressor:** Algoritmo ensemble que combina múltiplas árvores de decisão para criar predições mais robustas e precisas. Excelente para capturar relações não-lineares e interações entre variáveis.\n", "\n", "**Gradient Boosting:** Técnica de ensemble que constrói modelos sequencialmente, onde cada novo modelo corrige os erros do anterior. Muito eficaz para problemas de regressão complexos.\n", "\n", "**Feature Engineering:** Utilizaremos os clusters identificados na Fase 1 como features adici<PERSON><PERSON>, criando um modelo híbrido que aproveita tanto padrões não supervisionados quanto supervisionados.\n", "\n", "**Cross-Validation:** Técnica de validação que divide os dados em múltiplos folds para avaliar a performance do modelo de forma mais robusta e evitar overfitting.\n", "\n", "**Objetivo de Negócio:** Predizer a performance de vendas de óculos de grau em diferentes cidades para orientar decisões de expansão da rede de óticas especializadas."]}, {"cell_type": "code", "execution_count": 606, "id": "765123c7", "metadata": {}, "outputs": [], "source": ["# CORREÇÃO CRÍTICA: Evitar Data Leakage no Target\n", "# Redefinir target como métricas per store para evitar vazamento de informação\n", "\n", "# Métricas per capita/per store (intensidade, não volume absoluto)\n", "city_features['receita_per_store'] = city_features['receita_total'] / city_features['num_lojas']\n", "city_features['clientes_per_store'] = city_features['num_clientes_unicos'] / city_features['num_lojas']\n", "city_features['volume_per_store'] = city_features['volume_total'] / city_features['num_lojas']\n", "city_features['transacoes_per_store'] = city_features['num_transacoes'] / city_features['num_lojas']"]}, {"cell_type": "code", "execution_count": 607, "id": "4af8bc8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 receita_total: 6 outliers detectados\n", "   • Outliers: ['BRASILIA', 'CAMPINAS', 'CAMPO GRANDE', 'CURITIBA', 'MANAUS', 'RIO DE JANEIRO']\n", "📊 volume_total: 4 outliers detectados\n", "   • Outliers: ['CAMPINAS', 'CAMPO GRANDE', 'CURITIBA', 'RIO DE JANEIRO']\n", "\n", "Total de cidades outliers detectadas: 6\n", "Cidades outliers: ['CAMPO GRANDE', 'CURITIBA', 'BRASILIA', 'CAMPINAS', 'MANAUS', 'RIO DE JANEIRO']\n", "\n", "Impacto dos outliers:\n", "• Outliers representam 5.2% das cidades\n", "• Mas 21.8% da receita total\n", "\n", "Dataset sem outliers: 110 cidades\n", "(Outliers removidos para treinar modelo mais generalizado)\n"]}], "source": ["# CORREÇÃO CRÍTICA: Tratamento Robusto de Outliers\n", "# Método estatístico para identificar e tratar outliers além de São Paulo\n", "\n", "def detect_outliers_statistical(city_features, columns=['receita_total', 'volume_total'], method='iqr'):\n", "    \"\"\"Detectar outliers usando método estatístico robusto\"\"\"\n", "    \n", "    outlier_cities = set()\n", "    \n", "    for column in columns:\n", "        if method == 'iqr':\n", "            Q1 = city_features[column].quantile(0.25)\n", "            Q3 = city_features[column].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - 1.5 * IQR\n", "            upper_bound = Q3 + 1.5 * IQR\n", "            \n", "            column_outliers = city_features[\n", "                (city_features[column] < lower_bound) | \n", "                (city_features[column] > upper_bound)\n", "            ]['Dim_Lojas.Cidade_Emp'].tolist()\n", "            \n", "        elif method == 'zscore':\n", "            z_scores = np.abs((city_features[column] - city_features[column].mean()) / city_features[column].std())\n", "            column_outliers = city_features[z_scores > 3]['Dim_Lojas.Cidade_Emp'].tolist()\n", "        \n", "        outlier_cities.update(column_outliers)\n", "        \n", "        print(f\"📊 {column}: {len(column_outliers)} outliers detectados\")\n", "        if column_outliers:\n", "            print(f\"   • Outliers: {column_outliers}\")\n", "    \n", "    return list(outlier_cities)\n", "\n", "# Detectar outliers estatisticamente\n", "outlier_cities = detect_outliers_statistical(city_features)\n", "\n", "print(f\"\\nTotal de cidades outliers detectadas: {len(outlier_cities)}\")\n", "print(f\"Cidades outliers: {outlier_cities}\")\n", "\n", "# Analisar impacto dos outliers\n", "if outlier_cities:\n", "    outlier_data = city_features[city_features['Dim_Lojas.Cidade_Emp'].isin(outlier_cities)]\n", "    normal_data = city_features[~city_features['Dim_Lojas.Cidade_Emp'].isin(outlier_cities)]\n", "    \n", "    print(f\"\\nImpacto dos outliers:\")\n", "    print(f\"• Outliers representam {len(outlier_data)/len(city_features)*100:.1f}% das cidades\")\n", "    print(f\"• Mas {outlier_data['receita_total'].sum()/city_features['receita_total'].sum()*100:.1f}% da receita total\")\n", "    \n", "    # Criar dataset sem outliers para comparação\n", "    city_features_no_outliers = normal_data.copy()\n", "    \n", "    print(f\"\\nDataset sem outliers: {len(city_features_no_outliers)} cidades\")\n", "    print(\"(Outliers removidos para treinar modelo mais generalizado)\")\n", "    \n", "else:\n", "    print(\"Nenhum outlier adicional detectado além de São Paulo\")\n", "    city_features_no_outliers = city_features.copy()"]}, {"cell_type": "code", "execution_count": 608, "id": "f7654ce6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features adicionais criadas:\n", "   • customer_efficiency: range [0.286, 1.000]\n", "   • revenue_concentration: range [0.004, 4.740]\n", "   • price_stability: range [0.001, 1.000]\n", "   • market_penetration: range [0.286, 1.000]\n", "   • efficiency_score: range [0.000, 1.000]\n", "   • transaction_density: range [1.000, 60.000]\n", "\n", "Dataset novo: 23 features totais\n"]}], "source": ["# MELHORIA: Engenharia de Features Avançada\n", "# Criar features mais robustas e <PERSON>is\n", "\n", "def engineer_advanced_features(city_features):\n", "    \"\"\"<PERSON><PERSON><PERSON> features mais robustas para predição\"\"\"\n", "    \n", "    city_features = city_features.copy()\n", "    \n", "    # 1. Features de eficiência e concentração\n", "    city_features['customer_efficiency'] = (\n", "        city_features['num_clientes_unicos'] / city_features['num_transacoes']\n", "    ).<PERSON>na(0)\n", "    \n", "    city_features['revenue_concentration'] = (\n", "        city_features['receita_per_store'] / city_features['receita_per_store'].mean()\n", "    )\n", "    \n", "    # 2. Features de estabilidade (inverso da variabilidade)\n", "    city_features['price_stability'] = 1 / (1 + city_features['variabilidade_ticket'].fillna(0))\n", "    \n", "    # 3. Features de mercado (relativas)\n", "    city_features['market_penetration'] = (\n", "        city_features['num_clientes_unicos'] / city_features['num_transacoes']\n", "    ).<PERSON>na(0)\n", "    \n", "    # 4. Features compostas\n", "    city_features['efficiency_score'] = (\n", "        city_features['customer_efficiency'] * city_features['price_stability']\n", "    )\n", "    \n", "    # 5. Features de densidade de operação\n", "    city_features['transaction_density'] = (\n", "        city_features['num_transacoes'] / city_features['num_lojas']\n", "    )\n", "    \n", "    print(\"Features adicionais criadas:\")\n", "    new_features = ['customer_efficiency', 'revenue_concentration', 'price_stability', \n", "                   'market_penetration', 'efficiency_score', 'transaction_density']\n", "    \n", "    for feature in new_features:\n", "        print(f\"   • {feature}: range [{city_features[feature].min():.3f}, {city_features[feature].max():.3f}]\")\n", "    \n", "    return city_features, new_features\n", "\n", "# Aplicar engenharia de features\n", "city_features_enhanced, new_feature_names = engineer_advanced_features(city_features_no_outliers)\n", "\n", "print(f\"\\nDataset novo: {city_features_enhanced.shape[1]} features totais\")"]}, {"cell_type": "code", "execution_count": 609, "id": "60a7390e", "metadata": {}, "outputs": [], "source": ["# Pesos para abrir novas lojas\n", "def create_expansion_target(city_data):\n", "    \"\"\"\n", "    Target de expansão ponderado por potencial de LENTES:\n", "    combina per-store base com share e intensidade de LENTES suavizados.\n", "    \"\"\"\n", "    need_cols = [\n", "        'receita_per_store','clientes_per_store','volume_per_store', 'price_stability'\n", "    ]\n", "    \n", "    missing = [c for c in need_cols if c not in city_data.columns]\n", "    if missing:\n", "        raise ValueError(f\"Faltam colunas para target: {missing}\")\n", "\n", "    # Normalizar componentes base\n", "    scaler = MinMaxScaler()\n", "    base_norm = scaler.fit_transform(city_data[['receita_per_store','clientes_per_store','volume_per_store']])\n", "    base_df = pd.DataFrame(base_norm, columns=['receita_per_store_n','clientes_per_store_n','volume_per_store_n'], index=city_data.index)\n", "\n", "    # Pesos\n", "    w = {\n", "        'receita_per_store_n': 0.30,\n", "        'clientes_per_store_n': 0.20,\n", "        'volume_per_store_n': 0.10,\n", "        'price_stability': 0.02\n", "    }\n", "\n", "    score = (\n", "        w['receita_per_store_n'] * base_df['receita_per_store_n'] +\n", "        w['clientes_per_store_n'] * base_df['clientes_per_store_n'] +\n", "        w['volume_per_store_n'] * base_df['volume_per_store_n'] +\n", "        w['price_stability'] * city_data['price_stability']\n", "    )\n", "    return pd.Series(score, index=city_data.index, name='performance_score')\n", "\n", "# Substituir target atual se desejar:\n", "city_features_enhanced['performance_score'] = create_expansion_target(city_features_enhanced)"]}, {"cell_type": "code", "execution_count": 610, "id": "25573767", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Features selecionadas: ['cluster', 'ticket_medio', 'transaction_density', 'customer_efficiency', 'efficiency_score', 'volume_medio_transacao']\n", "   Target: performance_score\n", "   Dataset limpo: 110 cidades × 6 features\n", "   Removidos: 0 registros com NaN)\n", "\n", "Correlações feature-target:\n", "   Ridge (Stable): R² Test=0.933 | R² CV=0.619 ± 0.550 | Overfit=-0.020\n", "   Gradient Boosting: R² Test=0.926 | R² CV=0.894 ± 0.057 | Overfit=0.073\n", "   Random Forest: R² Test=0.899 | R² CV=0.887 ± 0.061 | Overfit=0.077\n", "\n", "SELEÇÃO DO MODELO FINAL:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>r2_train</th>\n", "      <th>r2_test</th>\n", "      <th>r2_cv_mean</th>\n", "      <th>r2_cv_std</th>\n", "      <th>overfitting</th>\n", "      <th>estimator</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Ridge (Stable)</th>\n", "      <td>0.913252</td>\n", "      <td>0.933399</td>\n", "      <td>0.61872</td>\n", "      <td>0.549529</td>\n", "      <td>-0.020146</td>\n", "      <td>(StandardScaler(), Ridge(alpha=2.0))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Grad<PERSON></th>\n", "      <td>0.999854</td>\n", "      <td>0.926438</td>\n", "      <td>0.894199</td>\n", "      <td>0.057341</td>\n", "      <td>0.073416</td>\n", "      <td>([DecisionTreeRegressor(criterion='friedman_ms...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Random Forest</th>\n", "      <td>0.976346</td>\n", "      <td>0.899036</td>\n", "      <td>0.886786</td>\n", "      <td>0.061284</td>\n", "      <td>0.07731</td>\n", "      <td>(DecisionTreeRegressor(max_features=1.0, min_s...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   r2_train   r2_test r2_cv_mean r2_cv_std overfitting  \\\n", "Ridge (Stable)     0.913252  0.933399    0.61872  0.549529   -0.020146   \n", "Gradient Boosting  0.999854  0.926438   0.894199  0.057341    0.073416   \n", "Random Forest      0.976346  0.899036   0.886786  0.061284     0.07731   \n", "\n", "                                                           estimator  \n", "Ridge (Stable)                  (StandardScaler(), Ridge(alpha=2.0))  \n", "Gradient Boosting  ([DecisionTreeRegressor(criterion='friedman_ms...  \n", "Random Forest      (DecisionTreeRegressor(max_features=1.0, min_s...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MODELO SELECIONADO: Gradient Boosting\n", "TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>predicted_return</th>\n", "      <th>ticket_medio</th>\n", "      <th>num_lojas</th>\n", "      <th>cluster</th>\n", "      <th>regiao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>PARAUAPEBAS</td>\n", "      <td>PA</td>\n", "      <td>0.443175</td>\n", "      <td>196.27</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>CAXIAS DO SUL</td>\n", "      <td>RS</td>\n", "      <td>0.437789</td>\n", "      <td>388.84</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>PORTO VELHO</td>\n", "      <td>RO</td>\n", "      <td>0.396533</td>\n", "      <td>245.39</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>PONTA GROSSA</td>\n", "      <td>PR</td>\n", "      <td>0.361714</td>\n", "      <td>531.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>CAMPOS DOS GOYTACAZES</td>\n", "      <td>RJ</td>\n", "      <td>0.329700</td>\n", "      <td>616.74</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>SANTO ANDRÉ</td>\n", "      <td>SP</td>\n", "      <td>0.297153</td>\n", "      <td>495.37</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>SANTA MARIA</td>\n", "      <td>RS</td>\n", "      <td>0.296585</td>\n", "      <td>373.83</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>TABOÃO DA SERRA</td>\n", "      <td>SP</td>\n", "      <td>0.295179</td>\n", "      <td>382.52</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>JOINVILLE</td>\n", "      <td>SC</td>\n", "      <td>0.289750</td>\n", "      <td>411.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>BALNEÁRIO CAMBORIÚ</td>\n", "      <td>SC</td>\n", "      <td>0.282241</td>\n", "      <td>219.41</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>SAO JOSE DOS CAMPOS</td>\n", "      <td>SP</td>\n", "      <td>0.276389</td>\n", "      <td>420.26</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>SANTARÉM</td>\n", "      <td>PA</td>\n", "      <td>0.270068</td>\n", "      <td>517.59</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>CANOAS</td>\n", "      <td>RS</td>\n", "      <td>0.261816</td>\n", "      <td>340.02</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>VOTORANTIM</td>\n", "      <td>SP</td>\n", "      <td>0.254458</td>\n", "      <td>493.51</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>UBERABA</td>\n", "      <td>MG</td>\n", "      <td>0.252376</td>\n", "      <td>572.51</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  predicted_return  \\\n", "72             PARAUAPEBAS                   PA          0.443175   \n", "27           CAXIAS DO SUL                   RS          0.437789   \n", "81             PORTO VELHO                   RO          0.396533   \n", "78            PONTA GROSSA                   PR          0.361714   \n", "22   CAMPOS DOS GOYTACAZES                   RJ          0.329700   \n", "94             SANTO ANDRÉ                   SP          0.297153   \n", "92             SANTA MARIA                   RS          0.296585   \n", "108        TABOÃO DA SERRA                   SP          0.295179   \n", "51               JOINVILLE                   SC          0.289750   \n", "8       BALNEÁRIO CAMBORIÚ                   SC          0.282241   \n", "96     SAO JOSE DOS CAMPOS                   SP          0.276389   \n", "93                SANTARÉM                   PA          0.270068   \n", "23                  CANOAS                   RS          0.261816   \n", "115             VOTORANTIM                   SP          0.254458   \n", "110                UBERABA                   MG          0.252376   \n", "\n", "     ticket_medio  num_lojas  cluster   regiao  \n", "72         196.27          1        1    NORTE  \n", "27         388.84          1        1      SUL  \n", "81         245.39          1        1    NORTE  \n", "78         531.77          1        1      SUL  \n", "22         616.74          1        1  SUDESTE  \n", "94         495.37          2        1  SUDESTE  \n", "92         373.83          1        1      SUL  \n", "108        382.52          1        1  SUDESTE  \n", "51         411.77          1        1      SUL  \n", "8          219.41          1        1      SUL  \n", "96         420.26          2        1  SUDESTE  \n", "93         517.59          1        1    NORTE  \n", "23         340.02          1        1      SUL  \n", "115        493.51          1        1  SUDESTE  \n", "110        572.51          1        1  SUDESTE  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "CATEGORIZAÇÃO PARA INVESTIMENTO:\n", "   BAIXA PRIORIDADE - Retorno baixo: 66 cidades\n", "   CONSIDERÁVEL - Retorno moderado: 22 cidades\n", "   ALTA PRIORIDADE - Alto retorno + Alto ticket: 16 cidades\n", "   MÉDIA PRIORIDADE - Alto retorno + Ticket moderado: 6 cidades\n"]}], "source": ["# ETAPA 1: Features para predição de retorno de novas lojas\n", "# Features independentes do target de performance\n", "expansion_features = [\n", "    'cluster',\n", "    'ticket_medio',\n", "    'transaction_density',\n", "    'customer_efficiency',\n", "    'efficiency_score',\n", "    'volume_medio_transacao',\n", "]\n", "\n", "# Evitar features que são componentes diretos da receita\n", "\n", "X_expansion = city_features_enhanced[expansion_features].replace([np.inf, -np.inf], np.nan)\n", "y_expansion = city_features_enhanced['performance_score'].copy()\n", "\n", "valid_mask = ~(y_expansion.isna() | X_expansion.isna().any(axis=1))\n", "X_expansion = X_expansion[valid_mask].copy()\n", "y_expansion = y_expansion[valid_mask].copy()\n", "\n", "print(f\"   Features selecionadas: {expansion_features}\")\n", "print(f\"   Target: performance_score\")\n", "print(f\"   Dataset limpo: {X_expansion.shape[0]} cidades × {X_expansion.shape[1]} features\")\n", "print(f\"   Removidos: {sum(~valid_mask)} registros com NaN)\")\n", "\n", "print(\"\\nCorrelações feature-target:\")\n", "corr_df = pd.DataFrame({\n", "    'feature': expansion_features,\n", "    'corr': [X_expansion[f].corr(y_expansion) for f in expansion_features]\n", "}).assign(abs_corr=lambda d: d['corr'].abs()).sort_values('abs_corr', ascending=False)\n", "\n", "# ETAPA 2: Treinar modelo para predição de retorno\n", "# Divisão estratificada baseada em quartis do target (alinhar índices com X_selected)\n", "target_quartiles = pd.qcut(y_expansion, q=4, labels=[0,1,2,3])\n", "idx = X_expansion.index\n", "\n", "X_train_exp, X_test_exp, y_train_exp, y_test_exp = train_test_split(\n", "    X_expansion, y_expansion.loc[idx], test_size=0.25, random_state=42,\n", "    stratify=target_quartiles.loc[idx]\n", ")\n", "\n", "# Modelos otimizados para predição de retorno\n", "return_models = {\n", "    'Ridge (Stable)': Pipeline([('scaler', StandardScaler()), ('model', Ridge(alpha=2.0))]),\n", "    'Gradient Boosting': GradientBoostingRegressor(n_estimators=200, max_depth=3, learning_rate=0.05, random_state=42),\n", "    'Random Forest': RandomForestRegressor(n_estimators=400, max_depth=None, min_samples_split=4, random_state=42, n_jobs=-1)\n", "}\n", "\n", "return_results = {}\n", "for name, model in return_models.items():\n", "    if isinstance(model, Pipeline):\n", "        pipe = model\n", "        pipe.fit(X_train_exp, y_train_exp)\n", "        r2_train = pipe.score(X_train_exp, y_train_exp)\n", "        r2_test = pipe.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(pipe, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = pipe\n", "    else:\n", "        model.fit(X_train_exp, y_train_exp)\n", "        r2_train = model.score(X_train_exp, y_train_exp)\n", "        r2_test = model.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(model, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = model\n", "\n", "    return_results[name] = {\n", "        'r2_train': r2_train,\n", "        'r2_test': r2_test,\n", "        'r2_cv_mean': cv_scores.mean(),\n", "        'r2_cv_std': cv_scores.std(),\n", "        'overfitting': r2_train - r2_test,\n", "        'estimator': final_estimator\n", "    }\n", "    print(f\"   {name}: R² Test={r2_test:.3f} | R² CV={cv_scores.mean():.3f} ± {cv_scores.std():.3f} | Overfit={r2_train - r2_test:.3f}\")\n", "\n", "print(f\"\\nSELEÇÃO DO MODELO FINAL:\")\n", "results_df_exp = pd.DataFrame(return_results).T.round(3)\n", "display(results_df_exp)\n", "\n", "best_name = results_df_exp['r2_cv_mean'].astype(float).idxmax()\n", "best_model = return_results[best_name]['estimator']\n", "print(f\"\\nMODELO SELECIONADO: {best_name}\")\n", "\n", "# 7) Predições e ranking usando X_selected (mesmas colunas do treino)\n", "predicted_returns = best_model.predict(X_expansion)\n", "city_results = city_features_enhanced.loc[idx].copy()\n", "city_results['predicted_return'] = predicted_returns\n", "\n", "expansion_ranking = city_results.nlargest(15, 'predicted_return')[[\n", "    'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'predicted_return',\n", "    'ticket_medio', 'num_lojas', 'cluster', 'regiao'\n", "]]\n", "print(\"TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\")\n", "display(expansion_ranking)\n", "\n", "# Segmentação para decisão\n", "def categorize_investment_priority(predicted_return, ticket_medio):\n", "    \"\"\"Categorizar prioridade de investimento\"\"\"\n", "    if predicted_return >= np.percentile(predicted_returns, 80):\n", "        if ticket_medio >= np.median(city_results['ticket_medio']):\n", "            return \"ALTA PRIORIDADE - Alto retorno + Alto ticket\"\n", "        else:\n", "            return \"MÉDIA PRIORIDADE - Alto retorno + Ticket moderado\"\n", "    elif predicted_return >= np.percentile(predicted_returns, 60):\n", "        return \"CONSIDERÁVEL - Retorno moderado\"\n", "    else:\n", "        return \"BAIXA PRIORIDADE - Retorno baixo\"\n", "\n", "city_results['investment_priority'] = city_results.apply(\n", "    lambda row: categorize_investment_priority(row['predicted_return'], row['ticket_medio']), \n", "    axis=1\n", ")\n", "\n", "priority_summary = city_results['investment_priority'].value_counts()\n", "print(f\"\\nCATEGORIZAÇÃO PARA INVESTIMENTO:\")\n", "for category, count in priority_summary.items():\n", "    print(f\"   {category}: {count} cidades\")"]}, {"cell_type": "code", "execution_count": 611, "id": "1a58533d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Dados IBGE recomendados por cidade:\n", "   Demográficos:\n", "     • populacao_total\n", "     • densidade_demografica\n", "     • renda_per_capita\n", "     • escolaridade_media\n", "   Econômicos:\n", "     • pib_municipal_per_capita\n", "     • indice_gini\n", "     • percentual_jovens_18_34\n", "     • percentual_classe_media\n", "\n", "🏢 DADOS DE CONCORRÊNCIA:\n", "     • num_oticas_concorrentes_5km\n", "     • densidade_oticas_cidade\n", "     • market_share_estimado\n", "     • preco_medio_concorrencia\n", "     • num_shopping_centers\n", "     • fluxo_pedestres_estimado\n", "\n", "🗺️ DADOS GEOGRÁFICOS E LOCALIZAÇÃO:\n", "     • distancia_capital_km\n", "     • regiao_metropolitana\n", "     • porte_cidade\n", "     • cluster_economico_ibge\n", "     • aeroporto_presente\n", "     • universidades_count\n", "\n", "📋 TEMPLATE PARA COLETA DE DADOS:\n", "\n", "def enrich_dataset_external():\n", "    \"\"\"Template para integração com dados externos\"\"\"\n", "\n", "    # 1. Integração IBGE\n", "    ibge_data = pd.read_csv('dados_ibge_municipios.csv')\n", "\n", "    # 2. Dados de concorrência (Google Places API)\n", "    competition_data = fetch_competition_data(cities_list)\n", "\n", "    # 3. <PERSON><PERSON> geográ<PERSON>\n", "    geo_data = pd.read_csv('dados_geograficos.csv')\n", "\n", "    # 4. Merge com dataset principal\n", "    enriched_dataset = city_features.merge(ibge_data, on='cidade_codigo_ibge')\n", "    enriched_dataset = enriched_dataset.merge(competition_data, on='cidade_nome')\n", "    enriched_dataset = enriched_dataset.merge(geo_data, on='cidade_nome')\n", "\n", "    return enriched_dataset\n", "\n"]}], "source": ["# PRÓXIMOS PASSOS RECOMENDADOS: Enriquecimento de Dados\n", "# Roadmap para melhorar significativamente o modelo\n", "\n", "# 1. Enriquecimento com dados externos\n", "print(\"📊 Dados IBGE recomendados por cidade:\")\n", "ibge_features = [\n", "    'populacao_total', 'densidade_demografica', 'renda_per_capita',\n", "    'escolaridade_media', 'pib_municipal_per_capita', 'indice_gini',\n", "    'percentual_jovens_18_34', 'percentual_classe_media'\n", "]\n", "\n", "print(\"   Demográficos:\")\n", "for feature in ibge_features[:4]:\n", "    print(f\"     • {feature}\")\n", "print(\"   Econômicos:\")\n", "for feature in ibge_features[4:]:\n", "    print(f\"     • {feature}\")\n", "\n", "# 2. Dad<PERSON> de concorrência\n", "print(f\"\\n🏢 DADOS DE CONCORRÊNCIA:\")\n", "competition_features = [\n", "    'num_oticas_concorrentes_5km', 'densidade_oticas_cidade',\n", "    'market_share_estimado', 'preco_medio_concorrencia',\n", "    'num_shopping_centers', 'fluxo_pedestres_estimado'\n", "]\n", "\n", "for feature in competition_features:\n", "    print(f\"     • {feature}\")\n", "\n", "# 3. Dados de localização geográfica\n", "print(f\"\\n🗺️ DADOS GEOGRÁFICOS E LOCALIZAÇÃO:\")\n", "location_features = [\n", "    'distancia_capital_km', 'regiao_metropolitana', 'porte_cidade',\n", "    'cluster_economico_ibge', 'aeroporto_presente', 'universidades_count'\n", "]\n", "\n", "for feature in location_features:\n", "    print(f\"     • {feature}\")\n", "\n", "# 4. <PERSON><PERSON><PERSON> para coleta de dados\n", "print(f\"\\n📋 TEMPLATE PARA COLETA DE DADOS:\")\n", "print(\"\"\"\n", "def enrich_dataset_external():\n", "    \\\"\\\"\\\"Template para integração com dados externos\\\"\\\"\\\"\n", "    \n", "    # 1. Integração IBGE\n", "    ibge_data = pd.read_csv('dados_ibge_municipios.csv')\n", "    \n", "    # 2. Dados de concorrência (Google Places API)\n", "    competition_data = fetch_competition_data(cities_list)\n", "    \n", "    # 3. <PERSON><PERSON> geográ<PERSON>\n", "    geo_data = pd.read_csv('dados_geograficos.csv')\n", "    \n", "    # 4. Merge com dataset principal\n", "    enriched_dataset = city_features.merge(ibge_data, on='cidade_codigo_ibge')\n", "    enriched_dataset = enriched_dataset.merge(competition_data, on='cidade_nome')\n", "    enriched_dataset = enriched_dataset.merge(geo_data, on='cidade_nome')\n", "    \n", "    return enriched_dataset\n", "\"\"\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}